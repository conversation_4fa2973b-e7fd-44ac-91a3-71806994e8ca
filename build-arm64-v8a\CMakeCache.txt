# This is the CMakeCache file.
# For build in directory: c:/Users/<USER>/Desktop/gadget/build-arm64-v8a
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//No help, variable specified on the command line.
ANDROID_ABI:UNINITIALIZED=arm64-v8a

//No help, variable specified on the command line.
ANDROID_NDK:UNINITIALIZED=C:\ndk\android-ndk-r27c

//No help, variable specified on the command line.
ANDROID_PLATFORM:UNINITIALIZED=android-21

//No help, variable specified on the command line.
CMAKE_ANDROID_ARCH_ABI:UNINITIALIZED=arm64-v8a

//No help, variable specified on the command line.
CMAKE_ANDROID_NDK:UNINITIALIZED=C:\ndk\android-ndk-r27c

//Archiver
CMAKE_AR:FILEPATH=C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-ar.exe

//Flags used by the compiler during all build types.
CMAKE_ASM_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_ASM_FLAGS_DEBUG:STRING=

//Flags used by the compiler during release builds.
CMAKE_ASM_FLAGS_RELEASE:STRING=

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Release

//Flags used by the compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=

//Flags used by the compiler during release builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=

//Flags used by the compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_C_FLAGS_DEBUG:STRING=

//Flags used by the compiler during release builds.
CMAKE_C_FLAGS_RELEASE:STRING=

//Flags used by the linker.
CMAKE_EXE_LINKER_FLAGS:STRING=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:UNINITIALIZED=ON

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/Users/<USER>/Desktop/gadget/build-arm64-v8a/CMakeFiles/pkgRedirects

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=CMAKE_MAKE_PROGRAM-NOTFOUND

//Flags used by the linker during the creation of modules.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=CustomGadget

//Ranlib
CMAKE_RANLIB:FILEPATH=C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-ranlib.exe

//Flags used by the linker during the creation of dll's.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Strip
CMAKE_STRIP:FILEPATH=C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-strip.exe

//No help, variable specified on the command line.
CMAKE_SYSTEM_NAME:UNINITIALIZED=Android

//No help, variable specified on the command line.
CMAKE_SYSTEM_VERSION:UNINITIALIZED=21

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=C:/ndk/android-ndk-r27c/build/cmake/android.toolchain.cmake

//Value Computed by CMake
CustomGadget_BINARY_DIR:STATIC=C:/Users/<USER>/Desktop/gadget/build-arm64-v8a

//Value Computed by CMake
CustomGadget_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
CustomGadget_SOURCE_DIR:STATIC=C:/Users/<USER>/Desktop/gadget


########################
# INTERNAL cache entries
########################

//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/Users/<USER>/Desktop/gadget/build-arm64-v8a
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=30
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=2
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake-gui.exe
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=MinGW Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/Users/<USER>/Desktop/gadget
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.30

