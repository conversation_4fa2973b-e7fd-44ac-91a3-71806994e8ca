
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:200 (message)"
      - "CMakeLists.txt:4 (project)"
    message: |
      The target system is: Android - 1 - aarch64
      The host system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
      Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security
      Id flags: -c;--target=aarch64-none-linux-android21 
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:47銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdC.so" -Wl,-soname="libCompilerIdC.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.65
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
      Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security
      Id flags:  
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:48銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdC.so" -Wl,-soname="libCompilerIdC.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.56
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
      Build flags: 
      Id flags: -c;--target=aarch64-none-linux-android21 
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:49銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdC.so" -Wl,-soname="libCompilerIdC.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.56
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:50銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdC.so" -Wl,-soname="libCompilerIdC.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdC\\CompilerIdC.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.60
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
      clang: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
      clang: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
      Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security
      Id flags: -c;--target=aarch64-none-linux-android21 
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:51銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCXXCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -fno-rtti -std=c++11 -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c++ CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdCXX.so" -Wl,-soname="libCompilerIdCXX.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCXXCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.58
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
      Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security
      Id flags:  
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:52銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCXXCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -fno-rtti -std=c++11 -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c++ CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdCXX.so" -Wl,-soname="libCompilerIdCXX.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCXXCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.63
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
      Build flags: 
      Id flags: -c;--target=aarch64-none-linux-android21 
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:52銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCXXCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -fno-rtti -std=c++11 -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c++ CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdCXX.so" -Wl,-soname="libCompilerIdCXX.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCXXCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.60
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
      鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:53銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      SetBuildDefaultEnvironmentVariables:
        ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
        ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
        ANT_HOME=
        JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
        NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -o "Debug\\\\CMakeCXXCompilerId.o" -w -O0 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -fno-rtti -std=c++11 -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -x c++ CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"Debug\\\\libCompilerIdCXX.so" -Wl,-soname="libCompilerIdCXX.so" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared Debug\\\\CMakeCXXCompilerId.o -lc++_static -lc++abi -llog -landroid
        ld: error: cannot open crtbegin_so.o: No such file or directory
        ld: error: unable to find library -llog
        ld: error: unable to find library -landroid
        ld: error: cannot open crtend_so.o: No such file or directory
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
      C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
      
      鐢熸垚澶辫触銆?
      
      鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣) (1) ->
      (Link 鐩爣) -> 
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\3.30.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj]
      
          0 涓鍛?
          1 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.58
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      clang++: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      clang++: error: no input files
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/gadget/build-test/CMakeFiles/CMakeScratch/TryCompile-5me35d"
      binary: "C:/Users/<USER>/Desktop/gadget/build-test/CMakeFiles/CMakeScratch/TryCompile-5me35d"
    cmakeVariables:
      ANDROID_ABI: "arm64-v8a"
      ANDROID_ARM_MODE: "thumb"
      ANDROID_PIE: "TRUE"
      ANDROID_PLATFORM: "android-21"
      ANDROID_STL: "c++_static"
      ANDROID_TOOLCHAIN: "clang"
      CMAKE_CXX_COMPILER_TARGET: "aarch64-none-linux-android21"
      CMAKE_C_COMPILER_TARGET: "aarch64-none-linux-android21"
      CMAKE_C_FLAGS: "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat "
      CMAKE_C_FLAGS_DEBUG: "-fno-limit-debug-info "
      CMAKE_EXE_LINKER_FLAGS: "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  "
      CMAKE_POSITION_INDEPENDENT_CODE: "TRUE"
      CMAKE_SYSROOT: "C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/sysroot"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/gadget/build-test/CMakeFiles/CMakeScratch/TryCompile-5me35d'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6d2ec.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
        鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:55銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-5me35d\\cmTC_6d2ec.vcxproj鈥?榛樿鐩爣)銆?
        SetBuildDefaultEnvironmentVariables:
          ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
          ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
          ANT_HOME=
          JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
          NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6d2ec.dir\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-5me35d\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6d2ec.dir\\Debug\\cmTC_6d2ec.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_6d2ec.dir\\Debug\\cmTC_6d2ec.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_6d2ec.dir\\Debug\\cmTC_6d2ec.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -gline-tables-only -o "cmTC_6d2ec.dir\\\\Debug\\\\CMakeCCompilerABI.o" -Wall -O3 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -D ANDROID -D _FORTIFY_SOURCE=2 -D "CMAKE_INTDIR=\\"Debug\\"" -x c  -g -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -Wformat -fno-limit-debug-info "C:\\\\Program Files\\\\CMake\\\\share\\\\cmake-3.30\\\\Modules\\\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"cmTC_6d2ec.dir\\\\Debug\\\\cmTC_6d2ec." -Wl,-soname="cmTC_6d2ec." -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared  -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments cmTC_6d2ec.dir\\\\Debug\\\\CMakeCCompilerABI.o -lc++_static -lc++abi -llog -landroid
          ld: error: cannot open crtbegin_so.o: No such file or directory
          ld: error: unable to find library -llog
          ld: error: unable to find library -landroid
          ld: error: cannot open crtend_so.o: No such file or directory
          clang: error: linker command failed with exit code 1 (use -v to see invocation)
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-5me35d\\cmTC_6d2ec.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-5me35d\\cmTC_6d2ec.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-5me35d\\cmTC_6d2ec.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-5me35d\\cmTC_6d2ec.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.60
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Check for working C compiler: C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe"
    directories:
      source: "C:/Users/<USER>/Desktop/gadget/build-test/CMakeFiles/CMakeScratch/TryCompile-hvqi16"
      binary: "C:/Users/<USER>/Desktop/gadget/build-test/CMakeFiles/CMakeScratch/TryCompile-hvqi16"
    cmakeVariables:
      ANDROID_ABI: "arm64-v8a"
      ANDROID_ARM_MODE: "thumb"
      ANDROID_PIE: "TRUE"
      ANDROID_PLATFORM: "android-21"
      ANDROID_STL: "c++_static"
      ANDROID_TOOLCHAIN: "clang"
      CMAKE_CXX_COMPILER_TARGET: "aarch64-none-linux-android21"
      CMAKE_C_COMPILER_TARGET: "aarch64-none-linux-android21"
      CMAKE_C_FLAGS: "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security "
      CMAKE_C_FLAGS_DEBUG: "-fno-limit-debug-info "
      CMAKE_EXE_LINKER_FLAGS: "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  "
      CMAKE_POSITION_INDEPENDENT_CODE: "TRUE"
      CMAKE_SYSROOT: "C:/ndk/android-ndk-r27c/toolchains/llvm/prebuilt/windows-x86_64/sysroot"
    buildResult:
      variable: "CMAKE_C_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/gadget/build-test/CMakeFiles/CMakeScratch/TryCompile-hvqi16'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_53033.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.10.4+10fbfbf2e
        鐢熸垚鍚姩鏃堕棿涓?2025/7/30 20:45:55銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-hvqi16\\cmTC_53033.vcxproj鈥?榛樿鐩爣)銆?
        SetBuildDefaultEnvironmentVariables:
          ANDROID_HOME=C:\\Program Files (x86)\\Android\\android-sdk
          ANDROID_SDK_ROOT=C:\\Program Files (x86)\\Android\\android-sdk
          ANT_HOME=
          JAVA_HOME=C:\\Program Files\\Android\\jdk\\jdk-8.0.302.8-hotspot\\jdk8u302-b08
          NDK_ROOT=C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_53033.dir\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-hvqi16\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_53033.dir\\Debug\\cmTC_53033.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_53033.dir\\Debug\\cmTC_53033.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_53033.dir\\Debug\\cmTC_53033.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -c -fdiagnostics-format=msvc --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -isystem "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++abi\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\include\\\\x86_64-linux-android" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\android\\\\support\\\\include" -I "C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\lib64\\\\clang\\\\12.0.9\\\\include" -gline-tables-only -o "cmTC_53033.dir\\\\Debug\\\\testCCompiler.o" -Wall -O3 -fno-strict-aliasing -fomit-frame-pointer -fno-exceptions -ffunction-sections -fdata-sections -fstack-protector -fpic -fno-short-enums -D __ANDROID_MIN_SDK_VERSION__=1 -D NDEBUG -D ANDROID -D _FORTIFY_SOURCE=2 -D "CMAKE_INTDIR=\\"Debug\\"" -x c  -g -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -Wformat -Werror=format-security -fno-limit-debug-info "C:\\\\Users\\\\<USER>\\\\Desktop\\\\gadget\\\\build-test\\\\CMakeFiles\\\\CMakeScratch\\\\TryCompile-hvqi16\\\\testCCompiler.c"
          testCCompiler.c
        Link:
          C:\\Program Files (x86)\\Android\\AndroidNDK\\android-ndk-r23c\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -fdiagnostics-format=msvc -o"cmTC_53033.dir\\\\Debug\\\\cmTC_53033." -Wl,-soname="cmTC_53033." -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-rpath-link="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot\\\\usr\\\\lib\\\\x86_64-linux-android\\\\1" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64\\\\lib\\\\gcc\\\\x86_64-linux-android\\\\4.9.x" -Wl,-L"C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\sources\\\\cxx-stl\\\\llvm-libc++\\\\libs\\\\x86_64" -Wl,--no-undefined --gcc-toolchain="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\x86_64-4.9\\\\prebuilt\\\\windows-x86_64" -target "x86_64-none-linux-android1" --sysroot="C:\\\\Program Files (x86)\\\\Android\\\\AndroidNDK\\\\android-ndk-r23c\\\\toolchains\\\\llvm\\\\prebuilt\\\\windows-x86_64\\\\sysroot" -Wl,-z,relro -Wl,-z,now -Wl,-z,noexecstack -shared  -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments cmTC_53033.dir\\\\Debug\\\\testCCompiler.o "-latomic" "-lm" -lc++_static -lc++abi -llog -landroid
          ld: error: cannot open crtbegin_so.o: No such file or directory
          ld: error: unable to find library -llog
          ld: error: unable to find library -landroid
          ld: error: cannot open crtend_so.o: No such file or directory
          clang: error: linker command failed with exit code 1 (use -v to see invocation)
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-hvqi16\\cmTC_53033.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-hvqi16\\cmTC_53033.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\23258\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-hvqi16\\cmTC_53033.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Microsoft\\VC\\v170\\Application Type\\Android\\3.0\\Android.Common.targets(125,5): error MSB6006: 鈥渃lang.exe鈥濆凡閫€鍑猴紝浠ｇ爜涓?1銆?[C:\\Users\\<USER>\\Desktop\\gadget\\build-test\\CMakeFiles\\CMakeScratch\\TryCompile-hvqi16\\cmTC_53033.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.60
        
      exitCode: 1
...
