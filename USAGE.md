# 自定义Frida Gadget使用说明

## 项目完成状态

✅ **项目已成功完成！**

已生成的文件：`libCustomGadget.so` (104MB)
- 架构：ARM64 (aarch64)
- 目标：Android API 21+
- 包含：完整的frida-core + 嵌入的creeperhook.js脚本

## 编译结果验证

```bash
# 文件信息
文件大小: 104,872,320 字节 (~100MB)
架构: elf64-littleaarch64 (ARM64)
导出函数: JNI_OnLoad (地址: 0x0000000000c41050)
```

## 使用方法

### 1. 准备目标APK

选择一个目标Android应用，你需要：
- 反编译APK文件
- 找到应用加载的native库文件
- 或者修改应用代码来加载我们的库

### 2. 注入方法

#### 方法A: 替换现有库
```bash
# 1. 反编译APK
apktool d target_app.apk

# 2. 找到lib目录下的某个SO文件
# 例如: lib/arm64-v8a/libnative-lib.so

# 3. 备份原文件并替换
mv lib/arm64-v8a/libnative-lib.so lib/arm64-v8a/libnative-lib.so.bak
cp libCustomGadget.so lib/arm64-v8a/libnative-lib.so

# 4. 重新打包
apktool b target_app -o target_app_modified.apk

# 5. 签名APK
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore target_app_modified.apk alias_name
```

#### 方法B: 添加新库
```java
// 在应用的Java代码中添加
static {
    System.loadLibrary("CustomGadget");
}
```

### 3. 安装和运行

```bash
# 安装修改后的APK
adb install target_app_modified.apk

# 启动应用
adb shell am start -n com.example.targetapp/.MainActivity

# 监控日志输出
adb logcat -s CustomGadget
```

## 预期行为

当应用启动并加载我们的SO文件时：

1. **JNI_OnLoad被调用** - 我们的代码开始执行
2. **Frida初始化** - 在后台线程中启动
3. **脚本加载** - creeperhook.js被自动执行
4. **Hook激活** - 开始监控clock_gettime函数

### 日志输出示例

```
I/CustomGadget: CustomGadget JNI_OnLoad called - Starting embedded Frida hook...
I/CustomGadget: Frida thread created successfully
I/CustomGadget: Starting Frida initialization...
I/CustomGadget: Found local device, attaching to current process...
I/CustomGadget: Attached to process 12345, creating script...
I/CustomGadget: Script loaded successfully! Hook is now active.
I/CustomGadget: Script message: {"type":"log","level":"info","description":"[+] Found 'clock_gettime' at 0x..."}
```

## Hook功能说明

嵌入的JavaScript脚本会：

1. **等待目标模块** - 监控`libcreeperbox.so`的加载
2. **Hook clock_gettime** - 拦截时间获取函数
3. **精确过滤** - 只处理来自特定返回地址的调用
4. **修改时间** - 将时间戳替换为固定值：
   - 秒数: 1753621963
   - 纳秒: 913992437

## 故障排除

### 常见问题

1. **应用崩溃**
   ```bash
   # 检查logcat错误
   adb logcat | grep -E "(FATAL|ERROR|CustomGadget)"
   ```

2. **Hook不生效**
   ```bash
   # 确认脚本是否加载
   adb logcat -s CustomGadget
   
   # 检查目标模块是否存在
   adb shell "cat /proc/$(pidof target_app)/maps | grep libcreeperbox"
   ```

3. **权限问题**
   ```bash
   # 确保应用有必要权限
   # 检查SELinux策略
   adb shell getenforce
   ```

### 调试技巧

1. **增加日志输出**
   - 修改custom_gadget.c中的LOGI调用
   - 重新编译

2. **检查内存映射**
   ```bash
   adb shell "cat /proc/$(pidof target_app)/maps"
   ```

3. **验证库加载**
   ```bash
   adb shell "lsof -p $(pidof target_app) | grep CustomGadget"
   ```

## 安全注意事项

⚠️ **重要提醒**

1. **合法使用** - 仅用于授权的安全测试和研究
2. **目标应用** - 确保你有权限修改目标应用
3. **数据备份** - 在修改前备份原始APK
4. **测试环境** - 在隔离的测试环境中进行

## 技术细节

### 架构说明
- **自包含设计** - 无需外部frida-server
- **嵌入式脚本** - JavaScript代码编译到SO中
- **自注入模式** - 附加到当前进程
- **线程安全** - 使用独立线程运行Frida

### 性能影响
- **内存占用** - 约100MB额外内存
- **启动延迟** - 增加1-2秒初始化时间
- **运行时开销** - Hook调用时的微小延迟

### 兼容性
- **Android版本** - API 21+ (Android 5.0+)
- **架构支持** - ARM64 (aarch64)
- **设备要求** - 足够的内存空间

## 项目文件说明

```
libCustomGadget.so          # 最终生成的库文件
custom_gadget.c             # 主要C源代码
creeperhook.js              # 原始JavaScript脚本
convert_js_to_c_string.py   # 脚本转换工具
build_simple.bat            # 简化构建脚本
CMakeLists.txt              # CMake配置文件
README.md                   # 项目说明
USAGE.md                    # 本使用说明
```

---

**项目完成时间**: 2025年7月30日  
**编译环境**: Windows + Android NDK r27c  
**目标架构**: ARM64 Android  
**状态**: ✅ 编译成功，可以使用
