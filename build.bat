@echo off
REM Build script for Custom Frida Gadget on Windows
REM This script compiles the custom gadget for Android using the NDK

setlocal enabledelayedexpansion

REM Configuration
set NDK_PATH=C:\ndk\android-ndk-r27c
set ANDROID_ABI=arm64-v8a
set ANDROID_API_LEVEL=21
set BUILD_TYPE=Release

echo === Custom Frida Gadget Build Script ===
echo.

REM Check if NDK exists
if not exist "%NDK_PATH%" (
    echo Error: Android NDK not found at %NDK_PATH%
    echo Please update the NDK_PATH variable in this script
    pause
    exit /b 1
)

REM Check if frida-core library exists
if not exist "frida\libfrida-core.a" (
    echo Error: frida-core library not found at frida\libfrida-core.a
    echo Please ensure you have compiled frida-core and placed it in the frida\ directory
    pause
    exit /b 1
)

REM Check if frida-core header exists
if not exist "frida\frida-core.h" (
    echo Error: frida-core header not found at frida\frida-core.h
    echo Please ensure you have the frida-core.h header in the frida\ directory
    pause
    exit /b 1
)

echo Configuration:
echo   NDK Path: %NDK_PATH%
echo   Android ABI: %ANDROID_ABI%
echo   API Level: %ANDROID_API_LEVEL%
echo   Build Type: %BUILD_TYPE%
echo.

REM Create build directory
set BUILD_DIR=build-%ANDROID_ABI%
echo Creating build directory: %BUILD_DIR%
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
mkdir "%BUILD_DIR%"
cd "%BUILD_DIR%"

REM Configure with CMake
echo Configuring with CMake...
cmake .. ^
    -DCMAKE_TOOLCHAIN_FILE="%NDK_PATH%\build\cmake\android.toolchain.cmake" ^
    -DANDROID_ABI="%ANDROID_ABI%" ^
    -DANDROID_PLATFORM="android-%ANDROID_API_LEVEL%" ^
    -DCMAKE_BUILD_TYPE="%BUILD_TYPE%" ^
    -DANDROID_NDK="%NDK_PATH%" ^
    -DCMAKE_ANDROID_ARCH_ABI="%ANDROID_ABI%" ^
    -DCMAKE_ANDROID_NDK="%NDK_PATH%" ^
    -DCMAKE_SYSTEM_NAME=Android ^
    -DCMAKE_SYSTEM_VERSION="%ANDROID_API_LEVEL%" ^
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

if errorlevel 1 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build
echo Building...
cmake --build . --config %BUILD_TYPE% --parallel

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

REM Check if the library was created
if exist "libCustomGadget.so" (
    echo Build successful!
    echo Output: %CD%\libCustomGadget.so
    
    REM Show file info
    echo Library information:
    dir libCustomGadget.so
    
    REM Copy to parent directory for convenience
    copy libCustomGadget.so ..\libCustomGadget-%ANDROID_ABI%.so
    echo Copied to: ..\libCustomGadget-%ANDROID_ABI%.so
) else (
    echo Build completed but library not found!
    pause
    exit /b 1
)

echo.
echo === Build Complete ===
echo.
echo Usage Instructions:
echo 1. Rename libCustomGadget-%ANDROID_ABI%.so to match a library your target app loads
echo 2. Replace the original library in the APK with this one
echo 3. Re-sign and install the APK
echo 4. The hook will activate automatically when the app loads the library
echo.
echo Note: Monitor logcat with tag 'CustomGadget' to see hook activity
echo.
pause
