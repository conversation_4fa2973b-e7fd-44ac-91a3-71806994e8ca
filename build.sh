#!/bin/bash

# Build script for Custom Frida Gadget
# This script compiles the custom gadget for Android using the NDK

set -e  # Exit on any error

# Configuration
NDK_PATH="C:/ndk/android-ndk-r27c"
ANDROID_ABI="arm64-v8a"  # Change to "armeabi-v7a" for 32-bit ARM
ANDROID_API_LEVEL="21"   # Minimum API level
BUILD_TYPE="Release"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Custom Frida Gadget Build Script ===${NC}"

# Check if NDK exists
if [ ! -d "$NDK_PATH" ]; then
    echo -e "${RED}Error: Android NDK not found at $NDK_PATH${NC}"
    echo "Please update the NDK_PATH variable in this script"
    exit 1
fi

# Check if frida-core library exists
if [ ! -f "frida/libfrida-core.a" ]; then
    echo -e "${RED}Error: frida-core library not found at frida/libfrida-core.a${NC}"
    echo "Please ensure you have compiled frida-core and placed it in the frida/ directory"
    exit 1
fi

# Check if frida-core header exists
if [ ! -f "frida/frida-core.h" ]; then
    echo -e "${RED}Error: frida-core header not found at frida/frida-core.h${NC}"
    echo "Please ensure you have the frida-core.h header in the frida/ directory"
    exit 1
fi

echo -e "${YELLOW}Configuration:${NC}"
echo "  NDK Path: $NDK_PATH"
echo "  Android ABI: $ANDROID_ABI"
echo "  API Level: $ANDROID_API_LEVEL"
echo "  Build Type: $BUILD_TYPE"
echo ""

# Create build directory
BUILD_DIR="build-$ANDROID_ABI"
echo -e "${YELLOW}Creating build directory: $BUILD_DIR${NC}"
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Configure with CMake
echo -e "${YELLOW}Configuring with CMake...${NC}"
cmake .. \
    -DCMAKE_TOOLCHAIN_FILE="$NDK_PATH/build/cmake/android.toolchain.cmake" \
    -DANDROID_ABI="$ANDROID_ABI" \
    -DANDROID_PLATFORM="android-$ANDROID_API_LEVEL" \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DANDROID_NDK="$NDK_PATH" \
    -DCMAKE_ANDROID_ARCH_ABI="$ANDROID_ABI" \
    -DCMAKE_ANDROID_NDK="$NDK_PATH" \
    -DCMAKE_SYSTEM_NAME=Android \
    -DCMAKE_SYSTEM_VERSION="$ANDROID_API_LEVEL" \
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON

if [ $? -ne 0 ]; then
    echo -e "${RED}CMake configuration failed!${NC}"
    exit 1
fi

# Build
echo -e "${YELLOW}Building...${NC}"
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo -e "${RED}Build failed!${NC}"
    exit 1
fi

# Check if the library was created
if [ -f "libCustomGadget.so" ]; then
    echo -e "${GREEN}Build successful!${NC}"
    echo -e "${GREEN}Output: $(pwd)/libCustomGadget.so${NC}"
    
    # Show file info
    echo -e "${YELLOW}Library information:${NC}"
    file libCustomGadget.so
    ls -lh libCustomGadget.so
    
    # Copy to parent directory for convenience
    cp libCustomGadget.so ../libCustomGadget-$ANDROID_ABI.so
    echo -e "${GREEN}Copied to: ../libCustomGadget-$ANDROID_ABI.so${NC}"
else
    echo -e "${RED}Build completed but library not found!${NC}"
    exit 1
fi

echo -e "${GREEN}=== Build Complete ===${NC}"
echo ""
echo -e "${YELLOW}Usage Instructions:${NC}"
echo "1. Rename libCustomGadget-$ANDROID_ABI.so to match a library your target app loads"
echo "2. Replace the original library in the APK with this one"
echo "3. Re-sign and install the APK"
echo "4. The hook will activate automatically when the app loads the library"
echo ""
echo -e "${YELLOW}Note:${NC} Monitor logcat with tag 'CustomGadget' to see hook activity"
