@echo off
REM Simple build script using NDK directly
REM This bypasses CMake Android toolchain issues

setlocal enabledelayedexpansion

REM Configuration
set NDK_PATH=C:\ndk\android-ndk-r27c
set ANDROID_ABI=arm64-v8a
set ANDROID_API_LEVEL=21
set BUILD_TYPE=Release

echo === Simple Custom Frida Gadget Build ===
echo.

REM Check if NDK exists
if not exist "%NDK_PATH%" (
    echo Error: Android NDK not found at %NDK_PATH%
    pause
    exit /b 1
)

REM Check if frida-core library exists
if not exist "frida\libfrida-core.a" (
    echo Error: frida-core library not found at frida\libfrida-core.a
    pause
    exit /b 1
)

REM Set up NDK toolchain paths
set TOOLCHAIN_PREFIX=%NDK_PATH%\toolchains\llvm\prebuilt\windows-x86_64
set CC=%TOOLCHAIN_PREFIX%\bin\aarch64-linux-android%ANDROID_API_LEVEL%-clang.cmd
set AR=%TOOLCHAIN_PREFIX%\bin\llvm-ar.exe
set STRIP=%TOOLCHAIN_PREFIX%\bin\llvm-strip.exe

REM Check if compiler exists
if not exist "%CC%" (
    echo Error: Compiler not found at %CC%
    pause
    exit /b 1
)

echo Configuration:
echo   NDK Path: %NDK_PATH%
echo   Compiler: %CC%
echo   Android ABI: %ANDROID_ABI%
echo   API Level: %ANDROID_API_LEVEL%
echo.

REM Create output directory
if not exist "output" mkdir output

REM Compile flags
set CFLAGS=-fPIC -ffunction-sections -fdata-sections -O2 -DANDROID -DNDEBUG
set CFLAGS=%CFLAGS% -I frida -I %TOOLCHAIN_PREFIX%\sysroot\usr\include
set CFLAGS=%CFLAGS% -Wall -Wextra

REM Link flags  
set LDFLAGS=-shared -Wl,--gc-sections -Wl,--export-dynamic
set LDFLAGS=%LDFLAGS% -L frida -lfrida-core
set LDFLAGS=%LDFLAGS% -llog -ldl -lm -pthread

REM Output file
set OUTPUT=output\libCustomGadget.so

echo Compiling custom_gadget.c...
"%CC%" %CFLAGS% %LDFLAGS% -o "%OUTPUT%" custom_gadget.c

if errorlevel 1 (
    echo Compilation failed!
    pause
    exit /b 1
)

REM Strip the binary to reduce size
echo Stripping binary...
"%STRIP%" "%OUTPUT%"

if exist "%OUTPUT%" (
    echo.
    echo === Build Successful ===
    echo Output: %OUTPUT%
    
    REM Show file info
    echo File information:
    dir "%OUTPUT%"
    
    echo.
    echo Usage Instructions:
    echo 1. Rename libCustomGadget.so to match a library your target app loads
    echo 2. Replace the original library in the APK with this one
    echo 3. Re-sign and install the APK
    echo 4. The hook will activate automatically when the app loads the library
    echo.
    echo Note: Monitor logcat with tag 'CustomGadget' to see hook activity
) else (
    echo Build failed - output file not created
    pause
    exit /b 1
)

echo.
pause
