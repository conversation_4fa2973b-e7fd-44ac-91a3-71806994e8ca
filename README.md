# Custom Frida Gadget with Embedded Script

这个项目创建了一个自包含的Android SO库，它将Frida核心功能和JavaScript hook脚本编译在一起。当SO文件被加载时，它会自动执行嵌入的Frida脚本，无需外部依赖或网络连接。

## 项目特点

- **自包含**: 将frida-core和JavaScript脚本编译成单个SO文件
- **自动执行**: 在JNI_OnLoad时自动初始化并执行hook脚本
- **无外部依赖**: 不需要frida-server或网络连接
- **嵌入式脚本**: JavaScript代码直接编译到SO文件中

## 文件结构

```
.
├── custom_gadget.c          # 主要的C代码，包含嵌入的JS脚本
├── CMakeLists.txt           # CMake构建配置
├── build.sh                 # Linux/macOS构建脚本
├── build.bat                # Windows构建脚本
├── convert_js_to_c_string.py # JS到C字符串转换工具
├── creeperhook.js           # 原始JavaScript hook脚本
├── frida/                   # Frida库文件目录
│   ├── frida-core.h         # Frida头文件
│   ├── libfrida-core.a      # Frida静态库
│   └── frida-core-example.c # Frida示例代码
└── README.md                # 本文件
```

## 构建要求

### 必需软件
- Android NDK r27c (位于 `C:\ndk\android-ndk-r27c`)
- CMake 3.18.1 或更高版本
- Python 3 (用于脚本转换)

### Frida库文件
确保在 `frida/` 目录下有以下文件：
- `frida-core.h` - Frida核心头文件
- `libfrida-core.a` - Frida静态库文件

## 构建步骤

### Windows系统
```batch
# 运行构建脚本
build.bat
```

### Linux/macOS系统
```bash
# 给脚本执行权限
chmod +x build.sh

# 运行构建脚本
./build.sh
```

### 手动构建
```bash
# 创建构建目录
mkdir build-arm64-v8a
cd build-arm64-v8a

# 配置CMake
cmake .. \
    -DCMAKE_TOOLCHAIN_FILE="C:/ndk/android-ndk-r27c/build/cmake/android.toolchain.cmake" \
    -DANDROID_ABI="arm64-v8a" \
    -DANDROID_PLATFORM="android-21" \
    -DCMAKE_BUILD_TYPE="Release"

# 编译
make -j$(nproc)
```

## 使用方法

### 1. 编译SO文件
运行构建脚本后，会生成 `libCustomGadget-arm64-v8a.so` 文件。

### 2. 注入到目标应用
有几种方法可以将SO文件注入到目标应用：

#### 方法1: 替换现有库
1. 反编译目标APK
2. 找到应用加载的某个SO文件
3. 将其替换为我们的 `libCustomGadget.so`
4. 重新打包并签名APK

#### 方法2: 修改应用加载逻辑
1. 修改应用的native代码，使其加载我们的SO
2. 重新编译应用

### 3. 监控执行
使用logcat监控hook的执行：
```bash
adb logcat -s CustomGadget
```

## 脚本功能

嵌入的JavaScript脚本 (`creeperhook.js`) 实现了以下功能：

1. **时间Hook**: Hook `clock_gettime` 函数
2. **模块检测**: 等待 `libcreeperbox.so` 模块加载
3. **精确拦截**: 只拦截来自特定返回地址的调用
4. **时间修改**: 将时间戳修改为固定值

## 自定义脚本

要使用不同的JavaScript脚本：

1. 修改 `creeperhook.js` 文件
2. 运行转换脚本：
   ```bash
   python convert_js_to_c_string.py creeperhook.js
   ```
3. 将输出的C字符串替换 `custom_gadget.c` 中的 `JS_SCRIPT` 变量
4. 重新编译

## 技术细节

### 架构
- 使用frida-core C API而不是Python绑定
- 在JNI_OnLoad中启动独立线程运行Frida
- 自注入模式：附加到当前进程而不是外部进程

### 线程模型
- 主线程：正常的应用执行
- Frida线程：运行GMainLoop和处理hook逻辑

### 内存管理
- 正确的GObject引用计数
- 清理所有Frida资源
- 线程安全的资源访问

## 故障排除

### 常见问题

1. **编译失败**
   - 检查NDK路径是否正确
   - 确保frida-core库文件存在
   - 验证CMake版本

2. **运行时崩溃**
   - 检查目标架构匹配 (arm64-v8a vs armeabi-v7a)
   - 确保所有依赖库都已链接
   - 查看logcat错误信息

3. **Hook不生效**
   - 确认目标模块已加载
   - 检查函数名和地址是否正确
   - 验证返回地址匹配逻辑

### 调试技巧
- 使用 `__android_log_print` 添加调试信息
- 监控logcat输出
- 使用GDB调试native代码

## 注意事项

1. **合法使用**: 仅用于合法的安全研究和测试
2. **兼容性**: 测试不同Android版本和设备
3. **性能**: 注意hook对应用性能的影响
4. **稳定性**: 充分测试以避免应用崩溃

## 许可证

本项目仅供学习和研究使用。请遵守相关法律法规。
