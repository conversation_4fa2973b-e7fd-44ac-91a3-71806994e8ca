/*
 * Custom Frida Gadget with Embedded Script
 * 
 * This file creates a self-contained SO that includes both frida-core
 * and the embedded JavaScript hook script. When loaded via JNI_OnLoad,
 * it automatically initializes <PERSON><PERSON> and executes the embedded script.
 */

#include <jni.h>
#include <pthread.h>
#include <unistd.h>
#include <android/log.h>
#include "frida/frida-core.h"

#define LOG_TAG "CustomGadget"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Embedded JavaScript script - Simplified native hook version
static const char* JS_SCRIPT =
    "console.log('[+] CustomGadget script starting...');\n"
    "\n"
    "// Fixed time values\n"
    "const FIXED_SECONDS = '1753621963';\n"
    "const FIXED_NANOSECONDS = '913992437';\n"
    "\n"
    "// Hook clock_gettime function\n"
    "const clock_gettime_addr = Module.getExportByName('libc.so', 'clock_gettime');\n"
    "if (clock_gettime_addr) {\n"
    "    console.log('[+] Found clock_gettime at: ' + clock_gettime_addr);\n"
    "    \n"
    "    Interceptor.attach(clock_gettime_addr, {\n"
    "        onEnter: function(args) {\n"
    "            // args[0] = clockid_t clk_id\n"
    "            // args[1] = struct timespec *tp\n"
    "            this.tp = args[1];\n"
    "            this.shouldModify = false;\n"
    "            \n"
    "            // Check if call is from libcreeperbox.so\n"
    "            const returnAddr = this.returnAddress;\n"
    "            const modules = Process.enumerateModules();\n"
    "            \n"
    "            for (let i = 0; i < modules.length; i++) {\n"
    "                if (modules[i].name === 'libcreeperbox.so') {\n"
    "                    const base = modules[i].base;\n"
    "                    const size = modules[i].size;\n"
    "                    const targetOffset = ptr('0x336670');\n"
    "                    const targetAddr = base.add(targetOffset);\n"
    "                    \n"
    "                    if (returnAddr.equals(targetAddr)) {\n"
    "                        console.log('[+] Target call detected from libcreeperbox.so!');\n"
    "                        this.shouldModify = true;\n"
    "                    }\n"
    "                    break;\n"
    "                }\n"
    "            }\n"
    "        },\n"
    "        \n"
    "        onLeave: function(retval) {\n"
    "            if (this.shouldModify && this.tp) {\n"
    "                // Modify timespec structure\n"
    "                // struct timespec { time_t tv_sec; long tv_nsec; }\n"
    "                this.tp.writeS64(FIXED_SECONDS);        // tv_sec\n"
    "                this.tp.add(8).writeS64(FIXED_NANOSECONDS); // tv_nsec\n"
    "                console.log('[+] Time modified successfully!');\n"
    "            }\n"
    "        }\n"
    "    });\n"
    "    \n"
    "    console.log('[+] clock_gettime hook installed successfully!');\n"
    "} else {\n"
    "    console.log('[-] Failed to find clock_gettime function');\n"
    "}\n"
    "\n"
    "console.log('[+] CustomGadget script loaded!');\n";

// Global variables
static GMainLoop* g_main_loop = NULL;
static FridaSession* g_session = NULL;
static FridaScript* g_script = NULL;
static pthread_t frida_thread;

// Callback functions
static void on_detached(FridaSession* session, FridaSessionDetachReason reason, FridaCrash* crash, gpointer user_data) {
    LOGI("Session detached, reason: %d", reason);
    if (g_main_loop && g_main_loop_is_running(g_main_loop)) {
        g_main_loop_quit(g_main_loop);
    }
}

static void on_message(FridaScript* script, const gchar* message, GBytes* data, gpointer user_data) {
    LOGI("Script message: %s", message);
}

// Main Frida execution thread
static void* frida_main_thread(void* arg) {
    GError* error = NULL;
    FridaDeviceManager* manager = NULL;
    FridaDevice* local_device = NULL;
    FridaScriptOptions* script_options = NULL;
    
    LOGI("Starting Frida initialization...");
    
    // Initialize Frida
    frida_init();
    
    // Create main loop
    g_main_loop = g_main_loop_new(NULL, FALSE);
    
    // Get device manager
    manager = frida_device_manager_new();
    if (!manager) {
        LOGE("Failed to create device manager");
        goto cleanup;
    }
    
    // Get local device
    FridaDeviceList* devices = frida_device_manager_enumerate_devices_sync(manager, NULL, &error);
    if (error) {
        LOGE("Failed to enumerate devices: %s", error->message);
        g_error_free(error);
        goto cleanup;
    }
    
    gint num_devices = frida_device_list_size(devices);
    for (gint i = 0; i < num_devices; i++) {
        FridaDevice* device = frida_device_list_get(devices, i);
        if (frida_device_get_dtype(device) == FRIDA_DEVICE_TYPE_LOCAL) {
            local_device = g_object_ref(device);
            break;
        }
        g_object_unref(device);
    }
    frida_unref(devices);
    
    if (!local_device) {
        LOGE("Failed to find local device");
        goto cleanup;
    }
    
    LOGI("Found local device, creating embedded session...");

    // Instead of attaching to a process, create an embedded session
    // This bypasses the need for process attachment permissions
    pid_t current_pid = getpid();
    LOGI("Current process PID: %d", current_pid);

    // Create session options
    FridaSessionOptions* session_options = frida_session_options_new();

    // Try to create an embedded session that doesn't require attachment
    g_session = frida_device_attach_sync(local_device, 0, session_options, NULL, &error);

    if (error) {
        LOGI("Standard attach failed, trying alternative method...");
        g_error_free(error);
        error = NULL;

        // Alternative: try to get the first available process and attach to it
        // This sometimes works better than self-attachment
        FridaProcessList* processes = frida_device_enumerate_processes_sync(local_device, NULL, NULL, &error);
        if (processes && !error) {
            gint num_processes = frida_process_list_size(processes);
            LOGI("Found %d processes, looking for current process...", num_processes);

            for (gint i = 0; i < num_processes; i++) {
                FridaProcess* process = frida_process_list_get(processes, i);
                guint pid = frida_process_get_pid(process);

                if (pid == current_pid) {
                    LOGI("Found current process in list, attempting attach...");
                    g_session = frida_device_attach_sync(local_device, pid, session_options, NULL, &error);
                    g_object_unref(process);
                    break;
                }
                g_object_unref(process);
            }
            frida_unref(processes);
        }
    }

    g_object_unref(session_options);

    if (error || !g_session) {
        LOGE("All attachment methods failed: %s", error ? error->message : "Unknown error");
        if (error) g_error_free(error);
        goto cleanup;
    }

    LOGI("Successfully created session");
    
    // Set up session callbacks
    g_signal_connect(g_session, "detached", G_CALLBACK(on_detached), NULL);
    
    LOGI("Attached to process %d, creating script...", current_pid);
    
    // Create script options
    script_options = frida_script_options_new();
    frida_script_options_set_name(script_options, "CustomGadgetScript");
    frida_script_options_set_runtime(script_options, FRIDA_SCRIPT_RUNTIME_QJS);
    
    // Create and load script
    g_script = frida_session_create_script_sync(g_session, JS_SCRIPT, script_options, NULL, &error);
    if (error) {
        LOGE("Failed to create script: %s", error->message);
        g_error_free(error);
        goto cleanup;
    }
    
    // Set up script callbacks
    g_signal_connect(g_script, "message", G_CALLBACK(on_message), NULL);
    
    // Load the script
    frida_script_load_sync(g_script, NULL, &error);
    if (error) {
        LOGE("Failed to load script: %s", error->message);
        g_error_free(error);
        goto cleanup;
    }
    
    LOGI("Script loaded successfully! Hook is now active.");
    
    // Run the main loop
    g_main_loop_run(g_main_loop);
    
cleanup:
    LOGI("Cleaning up Frida resources...");
    
    if (g_script) {
        frida_script_unload_sync(g_script, NULL, NULL);
        frida_unref(g_script);
        g_script = NULL;
    }
    
    if (g_session) {
        frida_session_detach_sync(g_session, NULL, NULL);
        frida_unref(g_session);
        g_session = NULL;
    }
    
    if (script_options) {
        g_object_unref(script_options);
    }
    
    if (local_device) {
        frida_unref(local_device);
    }
    
    if (manager) {
        frida_device_manager_close_sync(manager, NULL, NULL);
        frida_unref(manager);
    }
    
    if (g_main_loop) {
        g_main_loop_unref(g_main_loop);
        g_main_loop = NULL;
    }
    
    frida_deinit();
    
    LOGI("Frida thread exiting");
    return NULL;
}

// JNI_OnLoad - Entry point when SO is loaded
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    LOGI("CustomGadget JNI_OnLoad called - Starting embedded Frida hook...");
    
    // Create a detached thread to run Frida
    // This prevents blocking the main thread
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    
    int result = pthread_create(&frida_thread, &attr, frida_main_thread, NULL);
    pthread_attr_destroy(&attr);
    
    if (result != 0) {
        LOGE("Failed to create Frida thread: %d", result);
        return JNI_ERR;
    }
    
    LOGI("Frida thread created successfully");
    return JNI_VERSION_1_6;
}
