/*
 * Custom Frida Gadget with Embedded Script
 * 
 * This file creates a self-contained SO that includes both frida-core
 * and the embedded JavaScript hook script. When loaded via JNI_OnLoad,
 * it automatically initializes <PERSON><PERSON> and executes the embedded script.
 */

#include <jni.h>
#include <pthread.h>
#include <unistd.h>
#include <android/log.h>
#include "frida/frida-core.h"

#define LOG_TAG "CustomGadget"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Embedded JavaScript script (converted from creeperhook.js)
static const char* JS_SCRIPT = 
    "/*\n"
    " * anti_time_hook.js (v2 - Type Fix)\n"
    " *\n"
    " * This version fixes a critical bug where the script failed because it did not\n"
    " * use proper Int64 objects when writing to memory. This version correctly\n"
    " * converts the fixed timestamp values to Int64 before writing them.\n"
    " */\n"
    "\n"
    "\n"
    "Java.perform(function() {\n"
    "    const TARGET_MODULE = \"libcreeperbox.so\";\n"
    "    const LIBC_MODULE = \"libc.so\";\n"
    "    const FUNCTION_TO_HOOK = \"clock_gettime\";\n"
    "\n"
    "    // Correctly create Int64 objects for our fixed time.\n"
    "    const FIXED_SECONDS = int64(\"1753621963\"); \n"
    "    const FIXED_NANOSECONDS = int64(\"913992437\");\n"
    "\n"
    "    // Safely find the address of clock_gettime\n"
    "    let func_addr = null;\n"
    "    const libc_module = Process.getModuleByName(LIBC_MODULE);\n"
    "    if (!libc_module) return console.error(`[-] Could not find module: ${LIBC_MODULE}`);\n"
    "    \n"
    "    const exports = libc_module.enumerateExports();\n"
    "    for(let i=0; i < exports.length; i++) {\n"
    "        if(exports[i].name === FUNCTION_TO_HOOK) {\n"
    "            func_addr = exports[i].address;\n"
    "            break;\n"
    "        }\n"
    "    }\n"
    "    if (!func_addr) return console.error(`[-] Could not find export for function: ${0x77108c58c0}`);\n"
    "\n"
    "    console.log(`[+] Found '${0x77108c58c0}' at ${func_addr}. Waiting for ${TARGET_MODULE} to be loaded...`);\n"
    "\n"
    "    \n"
    "    function setupHookWhenModuleIsReady() {\n"
    "        const target_module = Process.getModuleByName(TARGET_MODULE);\n"
    "        if (target_module) {\n"
    "            // Module is loaded, stop polling.\n"
    "            clearInterval(waitForModuleInterval);\n"
    "\n"
    "            const offset = ptr(\"0x336670\");\n"
    "            const targetReturnAddress = target_module.base.add(offset);\n"
    "            console.log(`[+] Target module ${TARGET_MODULE} loaded at ${target_module.base}.`);\n"
    "            console.log(`[+] Waiting for calls from return address: ${targetReturnAddress}`);\n"
    "\n"
    "            // Attach to the function\n"
    "            Interceptor.attach(func_addr, {\n"
    "                onEnter: function (args) {\n"
    "                    // Fast check: compare the return address with our pre-calculated one.\n"
    "                    if (this.returnAddress.equals(targetReturnAddress)) {\n"
    "                        // Modify the structure pointed to by args[1]\n"
    "                        this.isTargetCall = true;\n"
    "                        this.tp = args[1]; \n"
    "                    }\n"
    "                },\n"
    "                onLeave: function (retval) {\n"
    "                    if (this.isTargetCall) {\n"
    "                        const arg_1 = this.tp;\n"
    "                        const arg_2 = this.tp.add(8); // On 64-bit, arg_2 is at offset 8\n"
    "                        console.log(`[+] success!!!`);\n"
    "                        arg_1.writeS64(FIXED_SECONDS);\n"
    "                        arg_2.writeS64(FIXED_NANOSECONDS);\n"
    "                    }\n"
    "                }\n"
    "            });\n"
    "\n"
    "            console.log(`[+] Hook for 0x77108c58c0 is now active.`);\n"
    "\n"
    "        }\n"
    "    }\n"
    "    \n"
    "    // Poll every 500ms until the target module is loaded.\n"
    "    const waitForModuleInterval = setInterval(setupHookWhenModuleIsReady, 500);\n"
    "\n"
    "})();\n";

// Global variables
static GMainLoop* g_main_loop = NULL;
static FridaSession* g_session = NULL;
static FridaScript* g_script = NULL;
static pthread_t frida_thread;

// Callback functions
static void on_detached(FridaSession* session, FridaSessionDetachReason reason, FridaCrash* crash, gpointer user_data) {
    LOGI("Session detached, reason: %d", reason);
    if (g_main_loop && g_main_loop_is_running(g_main_loop)) {
        g_main_loop_quit(g_main_loop);
    }
}

static void on_message(FridaScript* script, const gchar* message, GBytes* data, gpointer user_data) {
    LOGI("Script message: %s", message);
}

// Main Frida execution thread
static void* frida_main_thread(void* arg) {
    GError* error = NULL;
    FridaDeviceManager* manager = NULL;
    FridaDevice* local_device = NULL;
    FridaScriptOptions* script_options = NULL;
    
    LOGI("Starting Frida initialization...");
    
    // Initialize Frida
    frida_init();
    
    // Create main loop
    g_main_loop = g_main_loop_new(NULL, FALSE);
    
    // Get device manager
    manager = frida_device_manager_new();
    if (!manager) {
        LOGE("Failed to create device manager");
        goto cleanup;
    }
    
    // Get local device
    FridaDeviceList* devices = frida_device_manager_enumerate_devices_sync(manager, NULL, &error);
    if (error) {
        LOGE("Failed to enumerate devices: %s", error->message);
        g_error_free(error);
        goto cleanup;
    }
    
    gint num_devices = frida_device_list_size(devices);
    for (gint i = 0; i < num_devices; i++) {
        FridaDevice* device = frida_device_list_get(devices, i);
        if (frida_device_get_dtype(device) == FRIDA_DEVICE_TYPE_LOCAL) {
            local_device = g_object_ref(device);
            break;
        }
        g_object_unref(device);
    }
    frida_unref(devices);
    
    if (!local_device) {
        LOGE("Failed to find local device");
        goto cleanup;
    }
    
    LOGI("Found local device, attaching to current process...");
    
    // Attach to current process (self-injection)
    pid_t current_pid = getpid();
    g_session = frida_device_attach_sync(local_device, current_pid, NULL, NULL, &error);
    if (error) {
        LOGE("Failed to attach to process %d: %s", current_pid, error->message);
        g_error_free(error);
        goto cleanup;
    }
    
    // Set up session callbacks
    g_signal_connect(g_session, "detached", G_CALLBACK(on_detached), NULL);
    
    LOGI("Attached to process %d, creating script...", current_pid);
    
    // Create script options
    script_options = frida_script_options_new();
    frida_script_options_set_name(script_options, "CustomGadgetScript");
    frida_script_options_set_runtime(script_options, FRIDA_SCRIPT_RUNTIME_QJS);
    
    // Create and load script
    g_script = frida_session_create_script_sync(g_session, JS_SCRIPT, script_options, NULL, &error);
    if (error) {
        LOGE("Failed to create script: %s", error->message);
        g_error_free(error);
        goto cleanup;
    }
    
    // Set up script callbacks
    g_signal_connect(g_script, "message", G_CALLBACK(on_message), NULL);
    
    // Load the script
    frida_script_load_sync(g_script, NULL, &error);
    if (error) {
        LOGE("Failed to load script: %s", error->message);
        g_error_free(error);
        goto cleanup;
    }
    
    LOGI("Script loaded successfully! Hook is now active.");
    
    // Run the main loop
    g_main_loop_run(g_main_loop);
    
cleanup:
    LOGI("Cleaning up Frida resources...");
    
    if (g_script) {
        frida_script_unload_sync(g_script, NULL, NULL);
        frida_unref(g_script);
        g_script = NULL;
    }
    
    if (g_session) {
        frida_session_detach_sync(g_session, NULL, NULL);
        frida_unref(g_session);
        g_session = NULL;
    }
    
    if (script_options) {
        g_object_unref(script_options);
    }
    
    if (local_device) {
        frida_unref(local_device);
    }
    
    if (manager) {
        frida_device_manager_close_sync(manager, NULL, NULL);
        frida_unref(manager);
    }
    
    if (g_main_loop) {
        g_main_loop_unref(g_main_loop);
        g_main_loop = NULL;
    }
    
    frida_deinit();
    
    LOGI("Frida thread exiting");
    return NULL;
}

// JNI_OnLoad - Entry point when SO is loaded
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    LOGI("CustomGadget JNI_OnLoad called - Starting embedded Frida hook...");
    
    // Create a detached thread to run Frida
    // This prevents blocking the main thread
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    
    int result = pthread_create(&frida_thread, &attr, frida_main_thread, NULL);
    pthread_attr_destroy(&attr);
    
    if (result != 0) {
        LOGE("Failed to create Frida thread: %d", result);
        return JNI_ERR;
    }
    
    LOGI("Frida thread created successfully");
    return JNI_VERSION_1_6;
}
