#!/usr/bin/env python3
"""
Convert JavaScript file to C string format
"""

import sys
import re

def js_to_c_string(js_content):
    """Convert JavaScript content to C string format"""
    # Escape backslashes first
    content = js_content.replace('\\', '\\\\')
    # Escape double quotes
    content = content.replace('"', '\\"')
    # Split into lines and add C string formatting
    lines = content.split('\n')
    c_lines = []
    
    for i, line in enumerate(lines):
        if i == len(lines) - 1 and line.strip() == '':
            # Skip empty last line
            continue
        # Add line with proper C string formatting
        c_lines.append(f'    "{line}\\n"')
    
    return '\n'.join(c_lines)

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 convert_js_to_c_string.py <js_file>")
        sys.exit(1)
    
    js_file = sys.argv[1]
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        c_string = js_to_c_string(js_content)
        print("// Generated C string for JavaScript code:")
        print("static const char* JS_SCRIPT = ")
        print(c_string + ";")
        
    except FileNotFoundError:
        print(f"Error: File '{js_file}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
