@echo off
REM Verification script for the built Custom Gadget

echo === Custom Gadget Build Verification ===
echo.

set NDK_PATH=C:\ndk\android-ndk-r27c
set SO_FILE=libCustomGadget.so

REM Check if SO file exists
if not exist "%SO_FILE%" (
    echo ERROR: %SO_FILE% not found!
    echo Please run the build script first.
    pause
    exit /b 1
)

echo ✓ SO file found: %SO_FILE%

REM Get file size
for %%A in ("%SO_FILE%") do set FILE_SIZE=%%~zA
echo ✓ File size: %FILE_SIZE% bytes

REM Check file format
echo.
echo === File Format Information ===
"%NDK_PATH%\toolchains\llvm\prebuilt\windows-x86_64\bin\llvm-objdump.exe" -f "%SO_FILE%"

echo.
echo === Exported Symbols (JNI functions) ===
"%NDK_PATH%\toolchains\llvm\prebuilt\windows-x86_64\bin\llvm-nm.exe" -D "%SO_FILE%" | findstr JNI

echo.
echo === Dependencies ===
"%NDK_PATH%\toolchains\llvm\prebuilt\windows-x86_64\bin\llvm-objdump.exe" -p "%SO_FILE%" | findstr NEEDED

echo.
echo === Build Verification Complete ===
echo.
echo The library appears to be correctly built for Android ARM64.
echo File size: %FILE_SIZE% bytes (~100MB is expected)
echo.
echo Next steps:
echo 1. Use this SO file to replace a library in your target APK
echo 2. Re-sign and install the modified APK
echo 3. Monitor logcat for "CustomGadget" messages
echo.
pause
