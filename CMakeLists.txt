cmake_minimum_required(VERSION 3.18.1)

# Set C standard before project
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Android specific settings
if(ANDROID)
    set(CMAKE_ANDROID_STL_TYPE c++_shared)
endif()

# Project configuration
project(CustomGadget C)

# Build configuration
set(CMAKE_BUILD_TYPE Release)

# Compiler flags for optimization and Android compatibility
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -ffunction-sections -fdata-sections -fPIC")
set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O2 -DNDEBUG")

# Linker flags
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--gc-sections -Wl,--export-dynamic")

# Find required packages
find_package(PkgConfig REQUIRED)

# Set Frida installation path
# This should point to where you installed frida-core
set(FRIDA_INSTALL_DIR "${CMAKE_CURRENT_SOURCE_DIR}/frida" CACHE PATH "Path to Frida installation")

# Include directories
include_directories(
    ${FRIDA_INSTALL_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Find Frida core library
find_library(FRIDA_CORE_LIB
    NAMES frida-core libfrida-core
    PATHS ${FRIDA_INSTALL_DIR}
    NO_DEFAULT_PATH
    REQUIRED
)

# Create the shared library
add_library(CustomGadget SHARED
    custom_gadget.c
)

# Link libraries
target_link_libraries(CustomGadget
    ${FRIDA_CORE_LIB}
    log          # Android logging
    dl           # Dynamic linking
    m            # Math library
    pthread      # POSIX threads
)

# Set target properties
set_target_properties(CustomGadget PROPERTIES
    VERSION 1.0
    SOVERSION 1
    OUTPUT_NAME "CustomGadget"
)

# Android specific linking
if(ANDROID)
    target_link_libraries(CustomGadget
        android      # Android native API
    )
endif()

# Installation rules
install(TARGETS CustomGadget
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Print configuration information
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Frida install dir: ${FRIDA_INSTALL_DIR}")
message(STATUS "Frida core library: ${FRIDA_CORE_LIB}")
message(STATUS "Android ABI: ${ANDROID_ABI}")
message(STATUS "Android NDK: ${ANDROID_NDK}")

# Additional build information
if(ANDROID)
    message(STATUS "Target Android API: ${ANDROID_PLATFORM}")
    message(STATUS "Android STL: ${CMAKE_ANDROID_STL_TYPE}")
endif()
