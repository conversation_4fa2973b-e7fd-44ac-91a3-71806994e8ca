/*
 * anti_time_hook.js (v2 - Type Fix)
 *
 * This version fixes a critical bug where the script failed because it did not
 * use proper Int64 objects when writing to memory. This version correctly
 * converts the fixed timestamp values to Int64 before writing them.
 */


Java.perform(function() {
    const TARGET_MODULE = "libcreeperbox.so";
    const LIBC_MODULE = "libc.so";
    const FUNCTION_TO_HOOK = "clock_gettime";

    // Correctly create Int64 objects for our fixed time.
    const FIXED_SECONDS = int64("1753621963"); 
    const FIXED_NANOSECONDS = int64("913992437");

    // Safely find the address of clock_gettime
    let func_addr = null;
    const libc_module = Process.getModuleByName(LIBC_MODULE);
    if (!libc_module) return console.error(`[-] Could not find module: ${LIBC_MODULE}`);
    
    const exports = libc_module.enumerateExports();
    for(let i=0; i < exports.length; i++) {
        if(exports[i].name === FUNCTION_TO_HOOK) {
            func_addr = exports[i].address;
            break;
        }
    }
    if (!func_addr) return console.error(`[-] Could not find export for function: ${0x77108c58c0}`);

    console.log(`[+] Found '${0x77108c58c0}' at ${func_addr}. Waiting for ${TARGET_MODULE} to be loaded...`);

    
    function setupHookWhenModuleIsReady() {
        const target_module = Process.getModuleByName(TARGET_MODULE);
        if (target_module) {
            // Module is loaded, stop polling.
            clearInterval(waitForModuleInterval);

            const offset = ptr("0x336670");
            const targetReturnAddress = target_module.base.add(offset);
            console.log(`[+] Target module ${TARGET_MODULE} loaded at ${target_module.base}.`);
            console.log(`[+] Waiting for calls from return address: ${targetReturnAddress}`);

            // Attach to the function
            Interceptor.attach(func_addr, {
                onEnter: function (args) {
                    // Fast check: compare the return address with our pre-calculated one.
                    if (this.returnAddress.equals(targetReturnAddress)) {
                        // Modify the structure pointed to by args[1]
                        this.isTargetCall = true;
                        this.tp = args[1]; 
                    }
                },
                onLeave: function (retval) {
                    if (this.isTargetCall) {
                        const arg_1 = this.tp;
                        const arg_2 = this.tp.add(8); // On 64-bit, arg_2 is at offset 8
                        console.log(`[+] success!!!`);
                        arg_1.writeS64(FIXED_SECONDS);
                        arg_2.writeS64(FIXED_NANOSECONDS);
                    }
                }
            });

            console.log(`[+] Hook for 0x77108c58c0 is now active.`);

        }
    }
    
    // Poll every 500ms until the target module is loaded.
    const waitForModuleInterval = setInterval(setupHookWhenModuleIsReady, 500);

})();